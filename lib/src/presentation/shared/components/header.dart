import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/core/services/locator.dart';
import 'package:neorevv/src/presentation/cubit/auth/cubit/auth_cubit.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/utils/callback_functions.dart';
import '../../cubit/user/user_cubit.dart';

import '/src/core/utils/helper.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/config/tab_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/enum/user_role.dart';
import '../../../domain/models/user.dart';
import '../../screens/dashboard/components/mobile_drawer.dart';

class Header extends StatelessWidget {
  final List<TabConfig> tabs;
  final int selectedTabIndex;
  final Function(int) onTabSelected;
  final User? user;
  final VoidCallback? onMenuPressed;
  final StringCallback? onAddNewPressed;
  // final VoidCallback? onNotificationPressed;
  // final VoidCallback? onSettingsPressed;

  const Header({
    super.key,
    required this.tabs,
    required this.selectedTabIndex,
    required this.onTabSelected,
    required this.user,
    this.onMenuPressed,
    this.onAddNewPressed,
    // this.onNotificationPressed,
    // this.onSettingsPressed,
  });

  // Getter for the mobile drawer
  Widget get mobileDrawer => MobileDrawer(
    user: user,
    selectedTab: selectedTabIndex,
    onTabSelected: (index) {},
    selectedTabIndex: 0,
    tabs: [],
  );

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final user = context.watch<UserCubit>().state.user;
    final UserRole userRole = user?.role ?? UserRole.none;

    final bool isAdminOrOwner =
        userRole == UserRole.admin || userRole == UserRole.platformOwner;
    final bool isAdminOrBrokerageOrAgent = {
      UserRole.admin,
      UserRole.brokerage,
      UserRole.agent,
    }.contains(userRole);

    final bool isMobile = Responsive.isMobile(context);
    final bool showDrawer = Responsive.showDrawer(context);

    final bool desktopBreakpointGap =
        !isAdminOrOwner && width < headerGapBreakPoint;
    final bool tabletBreakpointGap = width < tabletBreakpoint;

    final bool removeProfileIfLogo =
        !isAdminOrOwner && (desktopBreakpointGap || tabletBreakpointGap);

    final double headerItemGap = tabletBreakpointGap
        ? defaultPadding / 2
        : defaultPadding;

    return Container(
      margin: EdgeInsets.only(top: isMobile ? 8 : defaultMargin),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 2,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(38),
              spreadRadius: 0,
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            if (showDrawer)
              Tooltip(
                message: AppStrings.menu,
                child: InkWell(
                  mouseCursor: SystemMouseCursors.click,
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  child: IconButton(
                    icon: const Icon(Icons.menu),
                    onPressed:
                        onMenuPressed ??
                        () => Scaffold.of(context).openDrawer(),
                  ),
                ),
              ),
            const SizedBox(width: 8),

            Image.asset('$launcherAssetpath/logo.png', scale: (154 / 40)),
            SizedBox(width: headerItemGap),

            if (!isAdminOrOwner) _buildUserOrgLogo(),
            const SizedBox(width: 5),

            if (!showDrawer) ...[
              ..._buildDesktopNavItems(context, userRole, headerItemGap),
              const Spacer(),

              if (isAdminOrBrokerageOrAgent)
                _buildAddNewBtnDropdown(context, headerItemGap, userRole),

              SizedBox(width: headerItemGap),
              //TODO implement this in future
              // _headerIcon(
              //   Icons.notifications_outlined,
              //   onPressed: onNotificationPressed,
              //   tooltipMessage: AppStrings.notifications,
              // ),
              // SizedBox(width: headerItemGap),
              // _headerIcon(
              //   Icons.settings_outlined,
              //   onPressed: onSettingsPressed,
              //   tooltipMessage: AppStrings.settings,
              // ),
              // SizedBox(width: headerItemGap),
            ],

            if (showDrawer) const Spacer(),

            if (!isMobile && !removeProfileIfLogo)
              _buildProfileInfo(context)
            else
              Row(
                children: [_buildUserAvatar(context), const SizedBox(width: 8)],
              ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildDesktopNavItems(
    BuildContext context,
    UserRole role,
    double gap,
  ) {
    return tabs.asMap().entries.map((entry) {
      final index = entry.key;
      final tab = entry.value;

      if (tab.hidden) return const SizedBox.shrink();
      // if (tab.title == AppStrings.reportsTab && role == UserRole.admin) {
      //   return const SizedBox.shrink();
      // }

      return _buildNavItem(
        context,
        tab.title,
        isSelected: selectedTabIndex == index,
        headerItemGap: gap,
        fontSize: 14,
        onTap: () => onTabSelected(index),
      );
    }).toList();
  }

  Widget _headerIcon(
    IconData icon, {
    VoidCallback? onPressed,
    String tooltipMessage = '',
  }) {
    return Tooltip(
      message: tooltipMessage,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          mouseCursor: SystemMouseCursors.click,
          onTap: onPressed,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.all(8.0),
            decoration: const BoxDecoration(
              color: AppTheme.headerIconBgColor,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 18, color: Colors.grey[600]),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    String title, {
    double headerItemGap = defaultPadding,
    double fontSize = 14,
    bool isSelected = false,
    required VoidCallback onTap,
  }) {
    return Visibility(
      visible: !Responsive.showDrawer(context),
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        mouseCursor: SystemMouseCursors.click,
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: headerItemGap / 2,
            vertical: 8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: isSelected
                    ? AppFonts.mediumTextStyle(
                        fontSize,
                        color: AppTheme.primaryColor,
                      )
                    : AppFonts.mediumTextStyle(fontSize, color: AppTheme.black),
              ),
              // TODO: Remove after implementing proper selection line
              // const SizedBox(height: 4),
              // Container(
              //   height: 2,
              //   width: 20,
              //   decoration: BoxDecoration(
              //     color: isSelected
              //         ? AppTheme.primaryColor
              //         : Colors.transparent,
              //     borderRadius: BorderRadius.circular(1),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddNewBtnDropdown(
    BuildContext context,
    double headerItemGap,
    UserRole role,
  ) {
    if (role == UserRole.admin) {
      return _buildAddNewBtn(headerItemGap, true, 'broker');
    } else if (role == UserRole.agent) {
      return _buildAddNewBtn(headerItemGap, true, 'agent');
    } else if (role == UserRole.brokerage) {
      return _CustomDropdownButton(
        parentWidth: MediaQuery.of(context).size.width,
        onSelected: (String value) {
          if (onAddNewPressed != null) {
            if (value == 'agent') {
              onAddNewPressed!('agent');
            } else if (value == 'office_staff') {
              onAddNewPressed!('office_staff');
            }
          } else {
            if (value == 'agent') {
              context.go(AppRoutes.registerAgent.path);
            } else if (value == 'office_staff') {
              context.go(AppRoutes.dashboard.path);
            }
          }
        },
        items: [
          _DropdownItem(
            value: 'agent',
            label: agentLabel,
            iconPath: '$iconAssetpath/user.png',
          ),
          _DropdownItem(
            value: 'office_staff',
            label: officeStaffLabel,
            iconPath: '$iconAssetpath/user.png',
          ),
        ],
        child: _buildAddNewBtn(headerItemGap, false, ''),
      );
    } else {
      return _buildAddNewBtn(headerItemGap, false, '');
    }
  }

  Container _buildAddNewBtn(
    double headerItemGap,
    bool enableTap,
    String navigationType,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          mouseCursor: SystemMouseCursors.click,
          onTap: enableTap
              ? () {
                  if (onAddNewPressed != null) {
                    onAddNewPressed!(navigationType);
                  }
                }
              : null,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: headerItemGap,
              vertical: defaultPadding / 2,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.add, size: 16, color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  addNewButton,
                  style: AppFonts.mediumTextStyle(12, color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInfo(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return _ProfileDropdownButton(
          parentWidth: MediaQuery.of(context).size.width,
          user: user,
          onProfileSelected: () {},
          onLogoutSelected: () async {
            context.read<AuthCubit>().logout();
          },

          child: Row(
            children: [
              _buildUserAvatar(context),
              const SizedBox(width: defaultPadding / 2),
              if (Responsive.isDesktop(context))
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user?.name ?? '',
                      style: AppFonts.semiBoldTextStyle(
                        14,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      user?.roleName ?? '',
                      style: AppFonts.regularTextStyle(
                        12,
                        color: Colors.grey[600]!,
                      ),
                    ),
                  ],
                ),
              if (Responsive.isDesktop(context))
                const Icon(Icons.keyboard_arrow_down),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUserAvatar(BuildContext context) {
    return _ProfileDropdownButton(
      parentWidth: MediaQuery.of(context).size.width,
      user: user,
      onProfileSelected: () {},
      onLogoutSelected: () async {
        context.read<AuthCubit>().logout();
      },
      child: FutureBuilder<bool>(
        future: isValidImageUrl(user?.avatarUrl ?? ''),
        builder: (context, snapshot) {
          bool isValid = snapshot.data ?? false;

          return Tooltip(
            message: AppStrings.profile,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                mouseCursor: SystemMouseCursors.click,
                child: CircleAvatar(
                  backgroundImage: isValid
                      ? CachedNetworkImageProvider(user!.avatarUrl)
                      : const AssetImage('$iconAssetpath/agent_round.png'),
                  radius: 16,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildUserOrgLogo() {
    return FutureBuilder<bool>(
      future: isValidImageUrl(user?.logoUrl ?? ''),
      builder: (context, snapshot) {
        bool isValid = snapshot.data ?? false;
        // if (!isValid) {
        //   return const SizedBox.shrink();
        // }
        return isValid
            ? CachedNetworkImage(
                imageUrl: user?.logoUrl ?? '',
                scale: (272 / 56),
              )
            : Image.asset('$imageAssetpath/org_logo.png', scale: (272 / 56));
      },
    );
  }
}

class _ProfileDropdownButton extends StatefulWidget {
  final double parentWidth;
  final User? user;
  final VoidCallback onProfileSelected;
  final VoidCallback onLogoutSelected;
  final Widget child;

  const _ProfileDropdownButton({
    super.key,
    required this.parentWidth,
    required this.user,
    required this.onProfileSelected,
    required this.onLogoutSelected,
    required this.child,
  });

  @override
  State<_ProfileDropdownButton> createState() => _ProfileDropdownButtonState();
}

class _ProfileDropdownButtonState extends State<_ProfileDropdownButton> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  void _showDropdown() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Material(
        type: MaterialType.transparency,
        child: Stack(
          children: [
            // Invisible barrier to close dropdown when clicking outside
            Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  mouseCursor: SystemMouseCursors.click,
                  onTap: _hideDropdown,
                  child: Container(color: Colors.transparent),
                ),
              ),
            ),
            // Dropdown menu
            Positioned(
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                targetAnchor: Alignment.bottomRight,
                followerAnchor: Alignment.topRight,
                offset: const Offset(0, 8),
                child: Material(
                  elevation: 8,
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    constraints: const BoxConstraints(
                      maxWidth: 160,
                      maxHeight: 120,
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: IntrinsicWidth(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildDropdownItemWithIcon(
                            'Profile',
                            Icons.person_outline,
                            widget.onProfileSelected,
                          ),
                          const SizedBox(height: 4),
                          _buildDropdownItemWithIcon(
                            'Logout',
                            Icons.logout,
                            widget.onLogoutSelected,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildDropdownItemWithIcon(
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return _SafeHoverItem(
      onTap: () {
        _hideDropdown();
        onTap();
      },
      child: Container(
        width: 130,
        constraints: const BoxConstraints(minHeight: 20, maxHeight: 40),
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: 8,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: AppTheme.tableDataFont),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                label,
                style: AppFonts.mediumTextStyle(12),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          mouseCursor: SystemMouseCursors.click,
          onTap: _showDropdown,
          child: widget.child,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }
}

class _DropdownItem {
  final String value;
  final String label;
  final String iconPath;

  _DropdownItem({
    required this.value,
    required this.label,
    required this.iconPath,
  });
}

class _CustomDropdownButton extends StatefulWidget {
  final Function(String) onSelected;
  final List<_DropdownItem> items;
  final Widget child;
  final double parentWidth;

  const _CustomDropdownButton({
    super.key,
    required this.onSelected,
    required this.items,
    required this.child,
    required this.parentWidth,
  });

  @override
  State<_CustomDropdownButton> createState() => _CustomDropdownButtonState();
}

class _CustomDropdownButtonState extends State<_CustomDropdownButton> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  void _showDropdown() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Invisible barrier to close dropdown when clicking outside
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                mouseCursor: SystemMouseCursors.click,
                onTap: _hideDropdown,
                child: Container(color: Colors.transparent),
              ),
            ),
          ),
          // Dropdown menu
          Positioned(
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: _getResponsiveOffset(widget.parentWidth),
              child: Material(
                elevation: 8,
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                child: Container(
                  margin: const EdgeInsets.only(
                    right: defaultMargin / 2,
                    left: defaultMargin / 2,
                  ),
                  constraints: const BoxConstraints(
                    maxWidth: 160,
                    maxHeight: 120,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: IntrinsicWidth(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: widget.items
                          .map((item) => _buildDropdownItem(item))
                          .toList(),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Offset _getResponsiveOffset(double screenWidth) {
    // Define breakpoints for different screen sizes
    // For add new button dropdown, we want it centered below the button
    if (screenWidth < 480) {
      // Small mobile: Position dropdown to avoid overflow
      return const Offset(-60, 35);
    } else if (screenWidth < 768) {
      // Mobile/tablet: Slightly to the left
      return const Offset(-40, 35);
    } else if (screenWidth < 1024) {
      // Tablet: Centered
      return const Offset(-20, 35);
    } else {
      // Desktop: Centered below button
      return const Offset(0, 35);
    }
  }

  Widget _buildDropdownItem(_DropdownItem item) {
    return _SafeHoverItem(
      onTap: () {
        _hideDropdown();
        widget.onSelected(item.value);
      },
      child: Container(
        width: 130,
        constraints: const BoxConstraints(minHeight: 20, maxHeight: 40),
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: 8,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              item.iconPath,
              width: 12,
              height: 12,
              color: AppTheme.tableDataFont,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                item.label,
                style: AppFonts.mediumTextStyle(12),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          mouseCursor: SystemMouseCursors.click,
          onTap: _showDropdown,
          child: widget.child,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _hideDropdown();
    super.dispose();
  }
}

class _SafeHoverItem extends StatefulWidget {
  final VoidCallback onTap;
  final Widget child;

  const _SafeHoverItem({super.key, required this.onTap, required this.child});

  @override
  State<_SafeHoverItem> createState() => _SafeHoverItemState();
}

class _SafeHoverItemState extends State<_SafeHoverItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        mouseCursor: SystemMouseCursors.click,
        onTap: widget.onTap,
        child: MouseRegion(
          onEnter: (_) {
            if (mounted) {
              setState(() => isHovered = true);
            }
          },
          onExit: (_) {
            if (mounted) {
              setState(() => isHovered = false);
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isHovered
                  ? AppTheme.headerIconBgColor
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }
}

class _ResponsivePopupMenuButton extends StatefulWidget {
  final String tooltip;
  final Offset offset;
  final Color color;
  final Function(String) onSelected;
  final List<PopupMenuEntry<String>> Function(BuildContext) itemBuilder;
  final ShapeBorder shape;
  final Widget child;

  const _ResponsivePopupMenuButton({
    super.key,
    required this.tooltip,
    required this.offset,
    required this.color,
    required this.onSelected,
    required this.itemBuilder,
    required this.shape,
    required this.child,
  });

  @override
  State<_ResponsivePopupMenuButton> createState() =>
      _ResponsivePopupMenuButtonState();
}

class _ResponsivePopupMenuButtonState
    extends State<_ResponsivePopupMenuButton> {
  bool _isMenuOpen = false;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      tooltip: widget.tooltip,
      offset: widget.offset,
      color: widget.color,
      onOpened: () => setState(() => _isMenuOpen = true),
      onCanceled: () => setState(() => _isMenuOpen = false),
      onSelected: (String value) {
        setState(() => _isMenuOpen = false);
        widget.onSelected(value);
      },
      itemBuilder: (BuildContext itemContext) {
        if (!mounted || !context.mounted) {
          return <PopupMenuEntry<String>>[];
        }
        return widget.itemBuilder(itemContext);
      },
      shape: widget.shape,
      child: widget.child,
    );
  }

  @override
  void dispose() {
    _isMenuOpen = false;
    super.dispose();
  }
}

class _CustomPopupMenuItem extends PopupMenuEntry<String> {
  final String value;
  final String iconPath;
  final String label;

  const _CustomPopupMenuItem({
    super.key,
    required this.value,
    required this.iconPath,
    required this.label,
  });

  @override
  double get height => 40;

  @override
  bool represents(String? value) => value == this.value;

  @override
  State<_CustomPopupMenuItem> createState() => _CustomPopupMenuItemState();
}

class _CustomPopupMenuItemState extends State<_CustomPopupMenuItem> {
  bool isHovered = false;

  void _updateHoverState(bool hovered) {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          try {
            setState(() => isHovered = hovered);
          } catch (e) {
            // Silently ignore setState errors during disposal
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        mouseCursor: SystemMouseCursors.click,
        onTap: () {
          if (context.mounted) {
            Navigator.of(context).pop(widget.value);
          }
        },
        child: MouseRegion(
          onEnter: (_) => _updateHoverState(true),
          onExit: (_) => _updateHoverState(false),
          child: Container(
            width: 10,
            height: 30,
            margin: const EdgeInsets.symmetric(horizontal: 10),
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 1.8,
              // vertical: 8,
            ),
            decoration: BoxDecoration(
              color: isHovered
                  ? AppTheme.headerIconBgColor
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Image.asset(
                  widget.iconPath,
                  width: 12,
                  height: 12,
                  color: AppTheme.tableDataFont,
                ),
                const SizedBox(width: 8),
                Text(widget.label, style: AppFonts.mediumTextStyle(12)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _HoverableMenuItem extends StatefulWidget {
  final String iconPath;
  final String label;

  const _HoverableMenuItem({
    super.key,
    required this.iconPath,
    required this.label,
  });

  @override
  State<_HoverableMenuItem> createState() => _HoverableMenuItemState();
}

class _HoverableMenuItemState extends State<_HoverableMenuItem> {
  bool isHovered = false;

  void _updateHoverState(bool hovered) {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && context.mounted) {
          try {
            setState(() => isHovered = hovered);
          } catch (e) {
            // Silently ignore setState errors during disposal
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _updateHoverState(true),
      onExit: (_) => _updateHoverState(false),
      child: Container(
        width: 140,
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isHovered ? AppTheme.headerIconBgColor : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Image.asset(
              widget.iconPath,
              width: 12,
              height: 12,
              color: AppTheme.tableDataFont,
            ),
            const SizedBox(width: 8),
            Text(widget.label, style: AppFonts.mediumTextStyle(12)),
          ],
        ),
      ),
    );
  }
}
