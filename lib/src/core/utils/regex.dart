class RegExUtils {
  RegExUtils._();

  static final RegExp usPhoneNumber = RegExp(r'^[0-9]{10}$');
  static final RegExp nonNumeric = RegExp(r'[^0-9]');
  static final RegExp emailRegex = RegExp(
    r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$',
  );
  static final zipCodeRegex = RegExp(r'^\d{5}(-\d{4})?$');
}
